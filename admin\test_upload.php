<?php
session_start();
include '../config.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['admin_logged_in'])) {
    header('Location: login.php');
    exit();
}

$message = '';
$error_message = '';

if ($_POST && isset($_FILES['test_image'])) {
    $uploadDir = '../assets/images/articles/';
    
    // التأكد من وجود المجلد
    if (!file_exists($uploadDir)) {
        if (!mkdir($uploadDir, 0777, true)) {
            $error_message = 'فشل في إنشاء مجلد الصور';
        }
    }
    
    if (!$error_message) {
        $fileExtension = strtolower(pathinfo($_FILES['test_image']['name'], PATHINFO_EXTENSION));
        $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        
        if (in_array($fileExtension, $allowedExtensions)) {
            if ($_FILES['test_image']['size'] <= 5 * 1024 * 1024) {
                $fileName = 'test_' . time() . '_' . rand(1000, 9999) . '.' . $fileExtension;
                $uploadPath = $uploadDir . $fileName;
                
                if (move_uploaded_file($_FILES['test_image']['tmp_name'], $uploadPath)) {
                    $message = 'تم رفع الصورة بنجاح: ' . $fileName;
                    
                    // حذف الصورة التجريبية بعد 5 ثوان
                    echo "<script>
                        setTimeout(function() {
                            fetch('delete_test_image.php?file=" . urlencode($fileName) . "');
                        }, 5000);
                    </script>";
                } else {
                    $error_message = 'فشل في رفع الصورة';
                }
            } else {
                $error_message = 'حجم الصورة كبير جداً (الحد الأقصى 5MB)';
            }
        } else {
            $error_message = 'نوع الملف غير مدعوم';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار رفع الصور</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { font-family: Arial; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1>اختبار رفع الصور</h1>
        
        <?php if ($message): ?>
        <div class="alert alert-success"><?php echo $message; ?></div>
        <?php endif; ?>
        
        <?php if ($error_message): ?>
        <div class="alert alert-danger"><?php echo $error_message; ?></div>
        <?php endif; ?>
        
        <div class="card">
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="test_image" class="form-label">اختر صورة للاختبار</label>
                        <input type="file" class="form-control" id="test_image" name="test_image" accept="image/*" required>
                    </div>
                    <button type="submit" class="btn btn-primary">رفع الصورة</button>
                </form>
            </div>
        </div>
        
        <div class="mt-3">
            <h3>معلومات النظام:</h3>
            <ul>
                <li>حد رفع الملفات: <?php echo ini_get('upload_max_filesize'); ?></li>
                <li>حد POST: <?php echo ini_get('post_max_size'); ?></li>
                <li>مجلد الرفع: <?php echo realpath('../assets/images/articles/'); ?></li>
                <li>صلاحيات المجلد: <?php echo is_writable('../assets/images/articles/') ? 'قابل للكتابة' : 'غير قابل للكتابة'; ?></li>
            </ul>
        </div>
        
        <div class="mt-3">
            <a href="articles.php" class="btn btn-secondary">العودة إلى إدارة المقالات</a>
            <a href="debug_articles.php" class="btn btn-info">تشخيص شامل</a>
        </div>
    </div>
</body>
</html>
