<?php
session_start();
include '../config.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['admin_logged_in'])) {
    header('Location: login.php');
    exit();
}

echo "<h1>تشخيص مشاكل المقالات</h1>";
echo "<style>body{font-family:Arial;direction:rtl;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";

// 1. فحص الاتصال بقاعدة البيانات
echo "<h2>1. فحص الاتصال بقاعدة البيانات</h2>";
try {
    $pdo->query("SELECT 1");
    echo "<p class='success'>✓ الاتصال بقاعدة البيانات يعمل بشكل صحيح</p>";
} catch(PDOException $e) {
    echo "<p class='error'>✗ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "</p>";
}

// 2. فحص وجود جدول المقالات
echo "<h2>2. فحص جدول المقالات</h2>";
try {
    $stmt = $pdo->query("DESCRIBE articles");
    $columns = $stmt->fetchAll();
    echo "<p class='success'>✓ جدول المقالات موجود</p>";
    echo "<p class='info'>الأعمدة الموجودة:</p>";
    echo "<ul>";
    foreach($columns as $column) {
        echo "<li>" . $column['Field'] . " (" . $column['Type'] . ")</li>";
    }
    echo "</ul>";
} catch(PDOException $e) {
    echo "<p class='error'>✗ جدول المقالات غير موجود أو به مشكلة: " . $e->getMessage() . "</p>";
    
    // محاولة إنشاء الجدول
    echo "<p class='info'>محاولة إنشاء الجدول...</p>";
    try {
        $pdo->exec("CREATE TABLE IF NOT EXISTS articles (
            id INT AUTO_INCREMENT PRIMARY KEY,
            title VARCHAR(255) NOT NULL,
            slug VARCHAR(255) UNIQUE NOT NULL,
            excerpt TEXT,
            content LONGTEXT NOT NULL,
            image VARCHAR(255),
            author VARCHAR(100) DEFAULT 'فريق نقرة',
            category VARCHAR(100) DEFAULT 'تسويق إلكتروني',
            tags TEXT,
            meta_title VARCHAR(255),
            meta_description TEXT,
            status ENUM('published', 'draft', 'archived') DEFAULT 'published',
            featured BOOLEAN DEFAULT FALSE,
            views INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        echo "<p class='success'>✓ تم إنشاء جدول المقالات بنجاح</p>";
    } catch(PDOException $e) {
        echo "<p class='error'>✗ فشل في إنشاء جدول المقالات: " . $e->getMessage() . "</p>";
    }
}

// 3. فحص صلاحيات مجلد الصور
echo "<h2>3. فحص مجلد الصور</h2>";
$uploadDir = '../assets/images/articles/';

if (!file_exists($uploadDir)) {
    echo "<p class='error'>✗ مجلد الصور غير موجود</p>";
    echo "<p class='info'>محاولة إنشاء المجلد...</p>";
    if (mkdir($uploadDir, 0777, true)) {
        echo "<p class='success'>✓ تم إنشاء مجلد الصور بنجاح</p>";
    } else {
        echo "<p class='error'>✗ فشل في إنشاء مجلد الصور</p>";
    }
} else {
    echo "<p class='success'>✓ مجلد الصور موجود</p>";
}

if (is_writable($uploadDir)) {
    echo "<p class='success'>✓ مجلد الصور قابل للكتابة</p>";
} else {
    echo "<p class='error'>✗ مجلد الصور غير قابل للكتابة</p>";
    echo "<p class='info'>محاولة تغيير الصلاحيات...</p>";
    if (chmod($uploadDir, 0777)) {
        echo "<p class='success'>✓ تم تغيير صلاحيات المجلد</p>";
    } else {
        echo "<p class='error'>✗ فشل في تغيير صلاحيات المجلد</p>";
    }
}

// 4. فحص إعدادات PHP
echo "<h2>4. فحص إعدادات PHP</h2>";
echo "<p class='info'>حد رفع الملفات: " . ini_get('upload_max_filesize') . "</p>";
echo "<p class='info'>حد POST: " . ini_get('post_max_size') . "</p>";
echo "<p class='info'>حد الذاكرة: " . ini_get('memory_limit') . "</p>";
echo "<p class='info'>حد وقت التنفيذ: " . ini_get('max_execution_time') . " ثانية</p>";

// 5. اختبار إضافة مقال تجريبي
echo "<h2>5. اختبار إضافة مقال تجريبي</h2>";
try {
    $test_title = "مقال تجريبي - " . date('Y-m-d H:i:s');
    $test_slug = "test-article-" . time();
    $test_content = "هذا محتوى تجريبي لاختبار إضافة المقالات";
    
    $stmt = $pdo->prepare("INSERT INTO articles (title, slug, content, author, status) VALUES (?, ?, ?, ?, ?)");
    $result = $stmt->execute([$test_title, $test_slug, $test_content, 'نظام التشخيص', 'draft']);
    
    if ($result) {
        $article_id = $pdo->lastInsertId();
        echo "<p class='success'>✓ تم إضافة مقال تجريبي بنجاح (ID: $article_id)</p>";
        
        // حذف المقال التجريبي
        $stmt = $pdo->prepare("DELETE FROM articles WHERE id = ?");
        $stmt->execute([$article_id]);
        echo "<p class='info'>تم حذف المقال التجريبي</p>";
    } else {
        echo "<p class='error'>✗ فشل في إضافة المقال التجريبي</p>";
    }
} catch(PDOException $e) {
    echo "<p class='error'>✗ خطأ في اختبار إضافة المقال: " . $e->getMessage() . "</p>";
}

// 6. فحص المقالات الموجودة
echo "<h2>6. المقالات الموجودة</h2>";
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM articles");
    $count = $stmt->fetch()['count'];
    echo "<p class='info'>عدد المقالات الموجودة: $count</p>";
    
    if ($count > 0) {
        $stmt = $pdo->query("SELECT id, title, status, created_at FROM articles ORDER BY created_at DESC LIMIT 5");
        $articles = $stmt->fetchAll();
        echo "<p class='info'>آخر 5 مقالات:</p>";
        echo "<ul>";
        foreach($articles as $article) {
            echo "<li>ID: {$article['id']} - {$article['title']} - {$article['status']} - {$article['created_at']}</li>";
        }
        echo "</ul>";
    }
} catch(PDOException $e) {
    echo "<p class='error'>✗ خطأ في جلب المقالات: " . $e->getMessage() . "</p>";
}

// 7. فحص ملف error log
echo "<h2>7. فحص سجل الأخطاء</h2>";
$error_log_path = ini_get('error_log');
if ($error_log_path && file_exists($error_log_path)) {
    echo "<p class='info'>مسار سجل الأخطاء: $error_log_path</p>";
    $errors = file_get_contents($error_log_path);
    $recent_errors = array_slice(explode("\n", $errors), -10);
    echo "<p class='info'>آخر 10 أخطاء:</p>";
    echo "<pre style='background:#f5f5f5;padding:10px;'>";
    foreach($recent_errors as $error) {
        if (trim($error)) {
            echo htmlspecialchars($error) . "\n";
        }
    }
    echo "</pre>";
} else {
    echo "<p class='info'>سجل الأخطاء غير متاح أو غير موجود</p>";
}

echo "<hr>";
echo "<p><strong>انتهى التشخيص</strong></p>";
echo "<div style='margin-top:20px;'>";
echo "<a href='articles.php' class='btn btn-primary' style='margin-left:10px;'>العودة إلى إدارة المقالات</a>";
echo "<a href='recreate_articles_table.php' class='btn btn-warning' style='margin-left:10px;'>إعادة إنشاء جدول المقالات</a>";
echo "<a href='test_database.php' class='btn btn-info'>اختبار قاعدة البيانات</a>";
echo "</div>";

// إضافة CSS للأزرار
echo "<style>
.btn {
    display: inline-block;
    padding: 8px 16px;
    margin: 4px;
    text-decoration: none;
    border-radius: 4px;
    font-weight: bold;
    text-align: center;
}
.btn-primary { background-color: #007bff; color: white; }
.btn-warning { background-color: #ffc107; color: black; }
.btn-info { background-color: #17a2b8; color: white; }
.btn:hover { opacity: 0.8; }
</style>";
?>
