# دليل حل مشاكل نظام إدارة المقالات

## المشاكل الشائعة وحلولها

### 1. لا يتم إضافة أو تعديل المقالات

**الأعراض:**
- عدم ظهور رسائل نجاح أو خطأ
- عدم حفظ البيانات في قاعدة البيانات
- الصفحة تعيد التحميل بدون تغيير

**الحلول:**

#### أ) فحص قاعدة البيانات
1. انتقل إلى `admin/comprehensive_test.php`
2. تحقق من حالة الاتصال بقاعدة البيانات
3. إذا كان هناك خطأ، تحقق من إعدادات `config.php`

#### ب) فحص جدول المقالات
1. انتقل إلى `admin/test_database.php`
2. تحقق من وجود جدول المقالات وبنيته
3. إذا كان الجدول مفقود أو به مشكلة، استخدم `admin/recreate_articles_table.php`

#### ج) فحص الأخطاء
1. انتقل إلى `admin/view_errors.php`
2. ابحث عن أخطاء PHP أو قاعدة البيانات
3. راجع سجل الأخطاء للحصول على تفاصيل أكثر

### 2. مشاكل رفع الصور

**الأعراض:**
- فشل في رفع الصور
- رسائل خطأ متعلقة بالصلاحيات
- الصور لا تظهر بعد الرفع

**الحلول:**

#### أ) فحص مجلد الصور
1. تأكد من وجود مجلد `assets/images/articles/`
2. تحقق من صلاحيات الكتابة (755 أو 777)
3. استخدم `admin/test_upload.php` لاختبار رفع الصور

#### ب) فحص إعدادات PHP
1. تحقق من `upload_max_filesize` (يجب أن يكون 5M أو أكثر)
2. تحقق من `post_max_size` (يجب أن يكون 8M أو أكثر)
3. تحقق من `max_execution_time` (يجب أن يكون 30 ثانية أو أكثر)

### 3. مشاكل الترميز والنصوص العربية

**الأعراض:**
- ظهور علامات استفهام بدلاً من النص العربي
- تشويه في النصوص العربية
- مشاكل في حفظ النصوص

**الحلول:**

#### أ) فحص ترميز قاعدة البيانات
1. تأكد من أن قاعدة البيانات تستخدم `utf8mb4_unicode_ci`
2. تأكد من أن الجداول تستخدم نفس الترميز
3. استخدم `admin/test_database.php` للتحقق

#### ب) فحص إعدادات PHP
1. تأكد من أن `default_charset` مضبوط على UTF-8
2. تحقق من headers في الصفحات

### 4. مشاكل الصلاحيات

**الأعراض:**
- رسائل "Permission denied"
- فشل في إنشاء الملفات أو المجلدات
- عدم القدرة على الكتابة في الملفات

**الحلول:**

#### أ) صلاحيات المجلدات
```bash
chmod 755 assets/
chmod 755 assets/images/
chmod 777 assets/images/articles/
```

#### ب) صلاحيات الملفات
```bash
chmod 644 admin/*.php
chmod 644 *.php
chmod 644 config.php
```

### 5. مشاكل الاستضافة المشتركة

**الأعراض:**
- قيود على استخدام بعض الوظائف
- مشاكل في الصلاحيات
- قيود على حجم قاعدة البيانات

**الحلول:**

#### أ) تحقق من قيود الاستضافة
1. راجع لوحة تحكم الاستضافة
2. تحقق من حدود قاعدة البيانات
3. تحقق من إعدادات PHP المتاحة

#### ب) تحسين الكود للاستضافة المشتركة
1. قلل من استخدام الذاكرة
2. استخدم استعلامات قاعدة بيانات محسنة
3. تجنب العمليات المكثفة

## أدوات التشخيص المتاحة

### 1. الاختبار الشامل
- الرابط: `admin/comprehensive_test.php`
- يفحص جميع جوانب النظام
- يعطي تقرير شامل عن الحالة

### 2. تشخيص المشاكل
- الرابط: `admin/debug_articles.php`
- يفحص قاعدة البيانات والملفات
- يعطي معلومات تفصيلية

### 3. اختبار قاعدة البيانات
- الرابط: `admin/test_database.php`
- يختبر الاتصال والجداول
- يفحص بنية قاعدة البيانات

### 4. اختبار رفع الصور
- الرابط: `admin/test_upload.php`
- يختبر رفع الملفات
- يفحص صلاحيات المجلدات

### 5. عرض سجل الأخطاء
- الرابط: `admin/view_errors.php`
- يعرض أخطاء PHP
- يساعد في تتبع المشاكل

## خطوات الإصلاح السريع

### إذا كان النظام لا يعمل نهائياً:

1. **فحص الاتصال بقاعدة البيانات**
   - تحقق من `config.php`
   - تأكد من صحة بيانات الاتصال

2. **إعادة إنشاء جدول المقالات**
   - استخدم `admin/recreate_articles_table.php`
   - سيحفظ البيانات الموجودة ويعيد إنشاء الجدول

3. **فحص الصلاحيات**
   - تأكد من صلاحيات المجلدات والملفات
   - أعط صلاحيات كتابة لمجلد الصور

4. **مراجعة سجل الأخطاء**
   - استخدم `admin/view_errors.php`
   - ابحث عن أخطاء محددة وحلها

### إذا كانت هناك مشاكل جزئية:

1. **استخدم الاختبار الشامل**
   - `admin/comprehensive_test.php`
   - سيحدد المشاكل بدقة

2. **اتبع التوصيات**
   - كل اختبار يعطي توصيات محددة
   - اتبع الخطوات المقترحة

3. **راجع هذا الدليل**
   - ابحث عن المشكلة المحددة
   - اتبع الحلول المقترحة

## الحصول على المساعدة

إذا لم تتمكن من حل المشكلة:

1. **اجمع المعلومات التالية:**
   - نتائج الاختبار الشامل
   - سجل الأخطاء
   - تفاصيل الاستضافة
   - خطوات إعادة إنتاج المشكلة

2. **تحقق من:**
   - إصدار PHP
   - إصدار MySQL
   - نوع الاستضافة
   - القيود المفروضة

3. **جرب الحلول البديلة:**
   - استخدم أدوات التشخيص المختلفة
   - جرب إعادة إنشاء الجداول
   - تحقق من إعدادات الخادم
