<?php
session_start();
include '../config.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['admin_logged_in'])) {
    header('Location: login.php');
    exit();
}

$message = '';
$error_message = '';

if (isset($_POST['recreate'])) {
    try {
        // إنشاء نسخة احتياطية من البيانات الموجودة
        $backup_data = [];
        try {
            $stmt = $pdo->query("SELECT * FROM articles");
            $backup_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $message .= "تم حفظ " . count($backup_data) . " مقال في النسخة الاحتياطية.<br>";
        } catch(PDOException $e) {
            $message .= "لا توجد بيانات للنسخ الاحتياطي.<br>";
        }

        // حذف الجدول القديم
        $pdo->exec("DROP TABLE IF EXISTS articles");
        $message .= "تم حذف الجدول القديم.<br>";

        // إنشاء الجدول الجديد بالبنية الصحيحة
        $create_table_sql = "
        CREATE TABLE `articles` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
            `slug` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
            `excerpt` text COLLATE utf8mb4_unicode_ci DEFAULT NULL,
            `content` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
            `image` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
            `author` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT 'فريق نقرة',
            `category` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT 'تسويق إلكتروني',
            `tags` text COLLATE utf8mb4_unicode_ci DEFAULT NULL,
            `meta_title` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
            `meta_description` text COLLATE utf8mb4_unicode_ci DEFAULT NULL,
            `status` enum('published','draft','archived') COLLATE utf8mb4_unicode_ci DEFAULT 'published',
            `featured` tinyint(1) DEFAULT '0',
            `views` int(11) DEFAULT '0',
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `slug` (`slug`),
            KEY `idx_status` (`status`),
            KEY `idx_featured` (`featured`),
            KEY `idx_category` (`category`),
            KEY `idx_created_at` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";

        $pdo->exec($create_table_sql);
        $message .= "تم إنشاء الجدول الجديد بنجاح.<br>";

        // استعادة البيانات
        if (!empty($backup_data)) {
            $restored_count = 0;
            foreach ($backup_data as $article) {
                try {
                    $stmt = $pdo->prepare("
                        INSERT INTO articles 
                        (title, slug, excerpt, content, image, author, category, tags, meta_title, meta_description, status, featured, views, created_at, updated_at) 
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ");
                    
                    $stmt->execute([
                        $article['title'] ?? '',
                        $article['slug'] ?? '',
                        $article['excerpt'] ?? null,
                        $article['content'] ?? '',
                        $article['image'] ?? null,
                        $article['author'] ?? 'فريق نقرة',
                        $article['category'] ?? 'تسويق إلكتروني',
                        $article['tags'] ?? null,
                        $article['meta_title'] ?? null,
                        $article['meta_description'] ?? null,
                        $article['status'] ?? 'published',
                        $article['featured'] ?? 0,
                        $article['views'] ?? 0,
                        $article['created_at'] ?? date('Y-m-d H:i:s'),
                        $article['updated_at'] ?? date('Y-m-d H:i:s')
                    ]);
                    $restored_count++;
                } catch(PDOException $e) {
                    // تجاهل الأخطاء في استعادة المقالات الفردية
                }
            }
            $message .= "تم استعادة $restored_count مقال من النسخة الاحتياطية.<br>";
        }

        // إضافة مقالات تجريبية إذا لم تكن هناك بيانات
        if (empty($backup_data)) {
            $sample_articles = [
                [
                    'title' => 'أهمية التسويق الإلكتروني في 2025',
                    'slug' => 'digital-marketing-importance-2025',
                    'excerpt' => 'التسويق الإلكتروني أصبح ضرورة حتمية لكل شركة تريد النجاح في العصر الرقمي.',
                    'content' => '<p>يشهد عالم التسويق الإلكتروني تطوراً مستمراً وسريعاً، وفي عام 2025 أصبح من الضروري لكل شركة أن تكون لها استراتيجية تسويق رقمي قوية.</p><h3>لماذا التسويق الإلكتروني مهم؟</h3><p>التسويق الإلكتروني يوفر للشركات إمكانية الوصول إلى جمهور أوسع بتكلفة أقل مقارنة بالتسويق التقليدي.</p>',
                    'featured' => 1
                ],
                [
                    'title' => 'كيفية تحسين موقعك لمحركات البحث',
                    'slug' => 'seo-website-optimization-guide',
                    'excerpt' => 'تعلم كيفية تحسين موقعك لمحركات البحث وزيادة الزيارات الطبيعية.',
                    'content' => '<p>تحسين محركات البحث (SEO) هو عملية تهدف إلى تحسين ظهور موقعك في نتائج البحث الطبيعية.</p><h3>أساسيات SEO:</h3><p>يتضمن SEO عدة عناصر مهمة مثل البحث عن الكلمات المفتاحية وتحسين المحتوى.</p>',
                    'featured' => 0
                ]
            ];

            foreach ($sample_articles as $article) {
                $stmt = $pdo->prepare("
                    INSERT INTO articles (title, slug, excerpt, content, featured, status) 
                    VALUES (?, ?, ?, ?, ?, 'published')
                ");
                $stmt->execute([
                    $article['title'],
                    $article['slug'],
                    $article['excerpt'],
                    $article['content'],
                    $article['featured']
                ]);
            }
            $message .= "تم إضافة مقالات تجريبية.<br>";
        }

        $message .= "<strong>تم إعادة إنشاء جدول المقالات بنجاح!</strong>";

    } catch(PDOException $e) {
        $error_message = "خطأ في إعادة إنشاء الجدول: " . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعادة إنشاء جدول المقالات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { font-family: Arial; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-database text-warning me-2"></i>إعادة إنشاء جدول المقالات</h1>
            <a href="articles.php" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i>العودة
            </a>
        </div>

        <?php if ($message): ?>
        <div class="alert alert-success">
            <?php echo $message; ?>
        </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
        <div class="alert alert-danger">
            <?php echo $error_message; ?>
        </div>
        <?php endif; ?>

        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>تحذير مهم
                </h5>
            </div>
            <div class="card-body">
                <p class="mb-3">
                    <strong>هذه العملية ستقوم بما يلي:</strong>
                </p>
                <ul>
                    <li>حفظ نسخة احتياطية من جميع المقالات الموجودة</li>
                    <li>حذف جدول المقالات الحالي</li>
                    <li>إنشاء جدول جديد بالبنية الصحيحة</li>
                    <li>استعادة المقالات من النسخة الاحتياطية</li>
                </ul>
                
                <div class="alert alert-warning mt-3">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تأكد من عمل نسخة احتياطية من قاعدة البيانات قبل المتابعة!</strong>
                </div>

                <form method="POST" onsubmit="return confirm('هل أنت متأكد من إعادة إنشاء جدول المقالات؟ هذه العملية لا يمكن التراجع عنها!')">
                    <button type="submit" name="recreate" class="btn btn-warning btn-lg">
                        <i class="fas fa-sync-alt me-2"></i>إعادة إنشاء الجدول
                    </button>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
