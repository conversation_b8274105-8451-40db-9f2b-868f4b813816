<?php
session_start();
include '../config.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['admin_logged_in'])) {
    header('Location: login.php');
    exit();
}

// تفعيل عرض الأخطاء للتشخيص
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// إنشاء ملف error log إذا لم يكن موجوداً
$error_log_file = '../error_log.txt';
if (!file_exists($error_log_file)) {
    file_put_contents($error_log_file, "بدء سجل الأخطاء - " . date('Y-m-d H:i:s') . "\n");
}

// قراءة آخر 50 سطر من سجل الأخطاء
$errors = [];
if (file_exists($error_log_file)) {
    $lines = file($error_log_file, FILE_IGNORE_NEW_LINES);
    $errors = array_slice($lines, -50);
    $errors = array_reverse($errors);
}

// مسح سجل الأخطاء إذا طُلب ذلك
if (isset($_GET['clear'])) {
    file_put_contents($error_log_file, "تم مسح سجل الأخطاء - " . date('Y-m-d H:i:s') . "\n");
    header('Location: view_errors.php');
    exit();
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض سجل الأخطاء</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { font-family: Arial; }
        .error-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            max-height: 500px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .error-line {
            margin-bottom: 5px;
            padding: 5px;
            border-left: 3px solid #dc3545;
            background: #fff;
        }
        .error-time {
            color: #6c757d;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-exclamation-triangle text-warning me-2"></i>سجل الأخطاء</h1>
            <div>
                <a href="view_errors.php" class="btn btn-primary btn-sm">
                    <i class="fas fa-refresh me-1"></i>تحديث
                </a>
                <a href="view_errors.php?clear=1" class="btn btn-danger btn-sm" onclick="return confirm('هل أنت متأكد من مسح سجل الأخطاء؟')">
                    <i class="fas fa-trash me-1"></i>مسح السجل
                </a>
                <a href="articles.php" class="btn btn-secondary btn-sm">
                    <i class="fas fa-arrow-right me-1"></i>العودة
                </a>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>آخر 50 خطأ
                    <small class="text-muted">(الأحدث أولاً)</small>
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($errors)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <p class="text-muted">لا توجد أخطاء مسجلة</p>
                    </div>
                <?php else: ?>
                    <div class="error-log">
                        <?php foreach ($errors as $error): ?>
                            <?php if (trim($error)): ?>
                                <div class="error-line">
                                    <?php
                                    // محاولة استخراج الوقت من بداية السطر
                                    if (preg_match('/^\[(.*?)\]/', $error, $matches)) {
                                        echo '<span class="error-time">[' . htmlspecialchars($matches[1]) . ']</span> ';
                                        echo htmlspecialchars(substr($error, strlen($matches[0]) + 1));
                                    } else {
                                        echo htmlspecialchars($error);
                                    }
                                    ?>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <div class="mt-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>معلومات النظام</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li><strong>PHP Version:</strong> <?php echo phpversion(); ?></li>
                                <li><strong>Error Reporting:</strong> <?php echo error_reporting(); ?></li>
                                <li><strong>Display Errors:</strong> <?php echo ini_get('display_errors') ? 'On' : 'Off'; ?></li>
                                <li><strong>Log Errors:</strong> <?php echo ini_get('log_errors') ? 'On' : 'Off'; ?></li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li><strong>Upload Max Size:</strong> <?php echo ini_get('upload_max_filesize'); ?></li>
                                <li><strong>Post Max Size:</strong> <?php echo ini_get('post_max_size'); ?></li>
                                <li><strong>Memory Limit:</strong> <?php echo ini_get('memory_limit'); ?></li>
                                <li><strong>Max Execution Time:</strong> <?php echo ini_get('max_execution_time'); ?>s</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تحديث تلقائي كل 30 ثانية
        setTimeout(function() {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
