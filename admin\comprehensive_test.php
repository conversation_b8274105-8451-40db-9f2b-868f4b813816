<?php
session_start();
include '../config.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['admin_logged_in'])) {
    header('Location: login.php');
    exit();
}

// تفعيل عرض الأخطاء
ini_set('display_errors', 1);
error_reporting(E_ALL);

$tests = [];
$overall_status = 'success';

// اختبار 1: الاتصال بقاعدة البيانات
try {
    $pdo->query("SELECT 1");
    $tests[] = ['name' => 'اتصال قاعدة البيانات', 'status' => 'success', 'message' => 'متصل بنجاح'];
} catch(Exception $e) {
    $tests[] = ['name' => 'اتصال قاعدة البيانات', 'status' => 'error', 'message' => $e->getMessage()];
    $overall_status = 'error';
}

// اختبار 2: وجود جدول المقالات
try {
    $stmt = $pdo->query("DESCRIBE articles");
    $columns = $stmt->fetchAll();
    if (count($columns) > 0) {
        $tests[] = ['name' => 'جدول المقالات', 'status' => 'success', 'message' => 'موجود مع ' . count($columns) . ' عمود'];
    } else {
        $tests[] = ['name' => 'جدول المقالات', 'status' => 'error', 'message' => 'الجدول فارغ'];
        $overall_status = 'error';
    }
} catch(Exception $e) {
    $tests[] = ['name' => 'جدول المقالات', 'status' => 'error', 'message' => 'غير موجود: ' . $e->getMessage()];
    $overall_status = 'error';
}

// اختبار 3: مجلد الصور
$upload_dir = '../assets/images/articles/';
if (!file_exists($upload_dir)) {
    mkdir($upload_dir, 0777, true);
}

if (is_writable($upload_dir)) {
    $tests[] = ['name' => 'مجلد الصور', 'status' => 'success', 'message' => 'موجود وقابل للكتابة'];
} else {
    $tests[] = ['name' => 'مجلد الصور', 'status' => 'error', 'message' => 'غير قابل للكتابة'];
    $overall_status = 'error';
}

// اختبار 4: إضافة مقال تجريبي
try {
    $test_title = "اختبار شامل - " . date('Y-m-d H:i:s');
    $test_slug = "comprehensive-test-" . time();
    $test_content = "هذا محتوى اختبار شامل للتأكد من عمل النظام";
    
    $stmt = $pdo->prepare("INSERT INTO articles (title, slug, content, status) VALUES (?, ?, ?, 'draft')");
    $result = $stmt->execute([$test_title, $test_slug, $test_content]);
    
    if ($result) {
        $article_id = $pdo->lastInsertId();
        $tests[] = ['name' => 'إضافة مقال', 'status' => 'success', 'message' => 'تم بنجاح (ID: ' . $article_id . ')'];
        
        // اختبار 5: تعديل المقال
        $updated_title = $test_title . " - محدث";
        $stmt = $pdo->prepare("UPDATE articles SET title = ? WHERE id = ?");
        $update_result = $stmt->execute([$updated_title, $article_id]);
        
        if ($update_result) {
            $tests[] = ['name' => 'تعديل مقال', 'status' => 'success', 'message' => 'تم التحديث بنجاح'];
        } else {
            $tests[] = ['name' => 'تعديل مقال', 'status' => 'error', 'message' => 'فشل في التحديث'];
            $overall_status = 'error';
        }
        
        // اختبار 6: جلب المقال
        $stmt = $pdo->prepare("SELECT * FROM articles WHERE id = ?");
        $stmt->execute([$article_id]);
        $article = $stmt->fetch();
        
        if ($article && $article['title'] == $updated_title) {
            $tests[] = ['name' => 'جلب مقال', 'status' => 'success', 'message' => 'تم جلب البيانات بنجاح'];
        } else {
            $tests[] = ['name' => 'جلب مقال', 'status' => 'error', 'message' => 'فشل في جلب البيانات'];
            $overall_status = 'error';
        }
        
        // اختبار 7: حذف المقال التجريبي
        $stmt = $pdo->prepare("DELETE FROM articles WHERE id = ?");
        $delete_result = $stmt->execute([$article_id]);
        
        if ($delete_result) {
            $tests[] = ['name' => 'حذف مقال', 'status' => 'success', 'message' => 'تم الحذف بنجاح'];
        } else {
            $tests[] = ['name' => 'حذف مقال', 'status' => 'error', 'message' => 'فشل في الحذف'];
            $overall_status = 'error';
        }
        
    } else {
        $tests[] = ['name' => 'إضافة مقال', 'status' => 'error', 'message' => 'فشل في الإضافة'];
        $overall_status = 'error';
    }
} catch(Exception $e) {
    $tests[] = ['name' => 'عمليات المقالات', 'status' => 'error', 'message' => $e->getMessage()];
    $overall_status = 'error';
}

// اختبار 8: إعدادات PHP
$upload_max = ini_get('upload_max_filesize');
$post_max = ini_get('post_max_size');
$memory_limit = ini_get('memory_limit');

$php_issues = [];
if (intval($upload_max) < 5) $php_issues[] = "حد رفع الملفات صغير ($upload_max)";
if (intval($post_max) < 8) $php_issues[] = "حد POST صغير ($post_max)";
if (intval($memory_limit) < 128) $php_issues[] = "حد الذاكرة صغير ($memory_limit)";

if (empty($php_issues)) {
    $tests[] = ['name' => 'إعدادات PHP', 'status' => 'success', 'message' => 'جميع الإعدادات مناسبة'];
} else {
    $tests[] = ['name' => 'إعدادات PHP', 'status' => 'warning', 'message' => implode(', ', $php_issues)];
    if ($overall_status == 'success') $overall_status = 'warning';
}

// اختبار 9: ملف get_article.php
if (file_exists('get_article.php')) {
    $tests[] = ['name' => 'ملف get_article.php', 'status' => 'success', 'message' => 'موجود'];
} else {
    $tests[] = ['name' => 'ملف get_article.php', 'status' => 'error', 'message' => 'غير موجود'];
    $overall_status = 'error';
}

// اختبار 10: صلاحيات الملفات
$files_to_check = ['articles.php', 'get_article.php', '../config.php'];
$permission_issues = [];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        if (!is_readable($file)) {
            $permission_issues[] = "$file غير قابل للقراءة";
        }
    } else {
        $permission_issues[] = "$file غير موجود";
    }
}

if (empty($permission_issues)) {
    $tests[] = ['name' => 'صلاحيات الملفات', 'status' => 'success', 'message' => 'جميع الملفات قابلة للوصول'];
} else {
    $tests[] = ['name' => 'صلاحيات الملفات', 'status' => 'error', 'message' => implode(', ', $permission_issues)];
    $overall_status = 'error';
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار شامل للمقالات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { font-family: Arial; }
        .test-item {
            display: flex;
            align-items: center;
            padding: 10px;
            margin-bottom: 5px;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
        .test-success { background-color: #d4edda; border-color: #c3e6cb; }
        .test-warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .test-error { background-color: #f8d7da; border-color: #f5c6cb; }
        .status-icon { margin-left: 10px; font-size: 1.2em; }
        .overall-status {
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 20px;
            font-size: 1.2em;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-clipboard-check text-primary me-2"></i>اختبار شامل للمقالات</h1>
            <div>
                <a href="comprehensive_test.php" class="btn btn-primary btn-sm">
                    <i class="fas fa-refresh me-1"></i>إعادة الاختبار
                </a>
                <a href="articles.php" class="btn btn-secondary btn-sm">
                    <i class="fas fa-arrow-right me-1"></i>العودة
                </a>
            </div>
        </div>

        <!-- الحالة العامة -->
        <div class="overall-status test-<?php echo $overall_status; ?>">
            <?php if ($overall_status == 'success'): ?>
                <i class="fas fa-check-circle text-success me-2"></i>
                جميع الاختبارات نجحت! النظام يعمل بشكل صحيح.
            <?php elseif ($overall_status == 'warning'): ?>
                <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                النظام يعمل مع بعض التحذيرات.
            <?php else: ?>
                <i class="fas fa-times-circle text-danger me-2"></i>
                يوجد مشاكل تحتاج إلى حل.
            <?php endif; ?>
        </div>

        <!-- نتائج الاختبارات -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-list me-2"></i>نتائج الاختبارات التفصيلية</h5>
            </div>
            <div class="card-body">
                <?php foreach ($tests as $test): ?>
                    <div class="test-item test-<?php echo $test['status']; ?>">
                        <div class="status-icon">
                            <?php if ($test['status'] == 'success'): ?>
                                <i class="fas fa-check-circle text-success"></i>
                            <?php elseif ($test['status'] == 'warning'): ?>
                                <i class="fas fa-exclamation-triangle text-warning"></i>
                            <?php else: ?>
                                <i class="fas fa-times-circle text-danger"></i>
                            <?php endif; ?>
                        </div>
                        <div class="flex-grow-1">
                            <strong><?php echo htmlspecialchars($test['name']); ?>:</strong>
                            <?php echo htmlspecialchars($test['message']); ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <?php if ($overall_status != 'success'): ?>
        <div class="mt-4">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-tools me-2"></i>أدوات الإصلاح</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2 d-md-flex">
                        <a href="recreate_articles_table.php" class="btn btn-warning">
                            <i class="fas fa-database me-1"></i>إعادة إنشاء جدول المقالات
                        </a>
                        <a href="debug_articles.php" class="btn btn-info">
                            <i class="fas fa-bug me-1"></i>تشخيص مفصل
                        </a>
                        <a href="view_errors.php" class="btn btn-danger">
                            <i class="fas fa-exclamation-triangle me-1"></i>عرض الأخطاء
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
