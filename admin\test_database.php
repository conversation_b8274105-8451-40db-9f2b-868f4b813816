<?php
session_start();
include '../config.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['admin_logged_in'])) {
    header('Location: login.php');
    exit();
}

$results = [];

// اختبار 1: الاتصال بقاعدة البيانات
try {
    $pdo->query("SELECT 1");
    $results[] = ['test' => 'اتصال قاعدة البيانات', 'status' => 'success', 'message' => 'متصل بنجاح'];
} catch(PDOException $e) {
    $results[] = ['test' => 'اتصال قاعدة البيانات', 'status' => 'error', 'message' => $e->getMessage()];
}

// اختبار 2: فحص جدول المقالات
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'articles'");
    if ($stmt->rowCount() > 0) {
        $results[] = ['test' => 'وجود جدول المقالات', 'status' => 'success', 'message' => 'الجدول موجود'];
        
        // فحص بنية الجدول
        $stmt = $pdo->query("DESCRIBE articles");
        $columns = $stmt->fetchAll();
        $column_names = array_column($columns, 'Field');
        $required_columns = ['id', 'title', 'slug', 'content', 'status', 'created_at'];
        $missing_columns = array_diff($required_columns, $column_names);
        
        if (empty($missing_columns)) {
            $results[] = ['test' => 'بنية جدول المقالات', 'status' => 'success', 'message' => 'جميع الأعمدة المطلوبة موجودة'];
        } else {
            $results[] = ['test' => 'بنية جدول المقالات', 'status' => 'warning', 'message' => 'أعمدة مفقودة: ' . implode(', ', $missing_columns)];
        }
    } else {
        $results[] = ['test' => 'وجود جدول المقالات', 'status' => 'error', 'message' => 'الجدول غير موجود'];
    }
} catch(PDOException $e) {
    $results[] = ['test' => 'فحص جدول المقالات', 'status' => 'error', 'message' => $e->getMessage()];
}

// اختبار 3: إدراج مقال تجريبي
try {
    $test_title = "مقال تجريبي - " . date('Y-m-d H:i:s');
    $test_slug = "test-" . time();
    $test_content = "محتوى تجريبي";
    
    $stmt = $pdo->prepare("INSERT INTO articles (title, slug, content, status) VALUES (?, ?, ?, ?)");
    $result = $stmt->execute([$test_title, $test_slug, $test_content, 'draft']);
    
    if ($result) {
        $article_id = $pdo->lastInsertId();
        $results[] = ['test' => 'إدراج مقال تجريبي', 'status' => 'success', 'message' => 'تم الإدراج بنجاح (ID: ' . $article_id . ')'];
        
        // حذف المقال التجريبي
        $stmt = $pdo->prepare("DELETE FROM articles WHERE id = ?");
        $stmt->execute([$article_id]);
        $results[] = ['test' => 'حذف المقال التجريبي', 'status' => 'success', 'message' => 'تم الحذف بنجاح'];
    } else {
        $results[] = ['test' => 'إدراج مقال تجريبي', 'status' => 'error', 'message' => 'فشل في الإدراج'];
    }
} catch(PDOException $e) {
    $results[] = ['test' => 'إدراج مقال تجريبي', 'status' => 'error', 'message' => $e->getMessage()];
}

// اختبار 4: فحص الترميز
try {
    $stmt = $pdo->query("SELECT @@character_set_database, @@collation_database");
    $charset_info = $stmt->fetch();
    if ($charset_info) {
        $charset = $charset_info['@@character_set_database'];
        $collation = $charset_info['@@collation_database'];
        if (strpos($charset, 'utf8') !== false) {
            $results[] = ['test' => 'ترميز قاعدة البيانات', 'status' => 'success', 'message' => "الترميز: $charset, التجميع: $collation"];
        } else {
            $results[] = ['test' => 'ترميز قاعدة البيانات', 'status' => 'warning', 'message' => "الترميز قد لا يدعم العربية: $charset"];
        }
    }
} catch(PDOException $e) {
    $results[] = ['test' => 'فحص الترميز', 'status' => 'error', 'message' => $e->getMessage()];
}

// اختبار 5: فحص المقالات الموجودة
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM articles");
    $count = $stmt->fetch()['count'];
    $results[] = ['test' => 'عدد المقالات الموجودة', 'status' => 'info', 'message' => "يوجد $count مقال"];
} catch(PDOException $e) {
    $results[] = ['test' => 'عدد المقالات', 'status' => 'error', 'message' => $e->getMessage()];
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار قاعدة البيانات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { font-family: Arial; }
        .test-result {
            margin-bottom: 10px;
            padding: 10px;
            border-radius: 5px;
        }
        .test-success { background-color: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .test-error { background-color: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .test-warning { background-color: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .test-info { background-color: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-database text-primary me-2"></i>اختبار قاعدة البيانات</h1>
            <div>
                <a href="test_database.php" class="btn btn-primary btn-sm">
                    <i class="fas fa-refresh me-1"></i>إعادة الاختبار
                </a>
                <a href="articles.php" class="btn btn-secondary btn-sm">
                    <i class="fas fa-arrow-right me-1"></i>العودة
                </a>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-list-check me-2"></i>نتائج الاختبارات</h5>
            </div>
            <div class="card-body">
                <?php foreach ($results as $result): ?>
                    <div class="test-result test-<?php echo $result['status']; ?>">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <?php if ($result['status'] == 'success'): ?>
                                    <i class="fas fa-check-circle text-success"></i>
                                <?php elseif ($result['status'] == 'error'): ?>
                                    <i class="fas fa-times-circle text-danger"></i>
                                <?php elseif ($result['status'] == 'warning'): ?>
                                    <i class="fas fa-exclamation-triangle text-warning"></i>
                                <?php else: ?>
                                    <i class="fas fa-info-circle text-info"></i>
                                <?php endif; ?>
                            </div>
                            <div class="flex-grow-1">
                                <strong><?php echo htmlspecialchars($result['test']); ?>:</strong>
                                <?php echo htmlspecialchars($result['message']); ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <div class="mt-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-cog me-2"></i>إعدادات قاعدة البيانات</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li><strong>Host:</strong> <?php echo DB_HOST; ?></li>
                                <li><strong>Database:</strong> <?php echo DB_NAME; ?></li>
                                <li><strong>User:</strong> <?php echo DB_USER; ?></li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <ul class="list-unstyled">
                                <li><strong>PDO Driver:</strong> <?php echo $pdo->getAttribute(PDO::ATTR_DRIVER_NAME); ?></li>
                                <li><strong>Server Version:</strong> <?php echo $pdo->getAttribute(PDO::ATTR_SERVER_VERSION); ?></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
