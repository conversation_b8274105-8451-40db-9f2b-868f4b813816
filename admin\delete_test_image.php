<?php
session_start();
include '../config.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['admin_logged_in'])) {
    http_response_code(401);
    exit();
}

$fileName = $_GET['file'] ?? '';

if ($fileName && strpos($fileName, 'test_') === 0) {
    $filePath = '../assets/images/articles/' . $fileName;
    if (file_exists($filePath)) {
        unlink($filePath);
        echo "تم حذف الصورة التجريبية";
    }
}
?>
